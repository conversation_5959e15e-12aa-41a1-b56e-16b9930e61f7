<template>
  <div class="monitor-screen">
    <!-- 监控画面切换按钮 -->
    <div class="flex-none mb-4 flex justify-center">
      <a-radio-group 
        v-model:value="monitorMode" 
        button-style="solid"
        @change="handleModeChange"
      >
        <a-radio-button value="live">实时画面</a-radio-button>
        <a-radio-button value="record">历史视频</a-radio-button>
      </a-radio-group>
    </div>

    <!-- 监控画面内容 -->
    <div class="">
      <!-- 实时监控画面 -->
      <div v-if="monitorMode === 'live'" class="space-y-4">
        <div v-if="liveStreams.length > 0">
          <div 
            v-for="stream in liveStreams" 
            :key="stream.key"
            class="monitor-item pb-2"
          >
            <!-- 视频播放器 -->
            <div class="video-container relative rounded-md overflow-hidden">
              <LivePlayer
                :key="stream.key"
                :path="stream.path"
                class="h-full w-full"
              />

              <div class="person-label absolute bottom-2 left-2 z-50 bg-white/70 rounded p-1">
                {{ stream.title }}
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-state">
          <span>暂无实时监控画面</span>
        </div>
      </div>

      <!-- 历史录像 -->
      <div v-else-if="monitorMode === 'record'" class="record-layout">
        <div v-if="recordedVideos.length > 0">
          <!-- 工作领导人标签 -->
          <div class="leader-label">
            工作领导人：{{ getLeaderName() }}
          </div>

          <!-- 主监控画面 -->
          <div class="main-monitor">
            <div v-if="selectedVideo" class="main-video-container">
              <video
                :src="selectedVideo.fileUrl"
                controls
                class="main-video"
                @loadedmetadata="handleVideoLoaded"
              >
                您的浏览器不支持视频播放
              </video>
            </div>
            <div v-else class="main-video-placeholder">
              <div class="placeholder-content">
                <PlayCircleOutlined class="placeholder-icon" />
                <div class="placeholder-text">选择历史录像进行播放</div>
              </div>
            </div>
          </div>

          <!-- 历史录像缩略图 -->
          <div class="history-thumbnails">
            <div
              v-for="(video, index) in displayVideos"
              :key="video.id"
              class="history-thumbnail"
              :class="{ active: selectedVideo?.id === video.id }"
              @click="handleVideoSelect(video)"
            >
              <div class="thumbnail-wrapper">
                <!-- <img
                  :src="video.thumbnailUrl || '/images/video-placeholder.png'"
                  alt=""
                  class="thumbnail-img"
                /> -->
                <!-- 播放按钮覆盖层 -->
                <div class="thumbnail-overlay">
                  <PlayCircleOutlined class="overlay-icon" />
                </div>
                <!-- 视频序号 -->
                <div class="thumbnail-number">
                  {{ index + 1 }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-state">
          <span>暂无历史录像数据</span>
        </div>
      </div>
    </div>

    <!-- 视频预览弹窗 -->
    <a-modal
      v-model:open="previewVisible"
      title="视频预览"
      :width="800"
      :footer="null"
      centered
    >
      <div v-if="currentPreviewVideo" class="preview-container">
        <video
          :src="currentPreviewVideo.fileUrl"
          controls
          class="w-full h-auto"
          style="max-height: 500px;"
        >
          您的浏览器不支持视频播放
        </video>
        <div class="mt-2 text-gray-600">
          {{ currentPreviewVideo.fileName }}
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted } from 'vue';
  import { Radio as ARadio, Modal as AModal } from 'ant-design-vue';
  import { PlayCircleOutlined } from '@ant-design/icons-vue';
  import LivePlayer from '@/components/LivePlayer/index.vue';
  import { postControlTaskGetLiveVideoUrl } from '@/api/security/railwayWorkflowTask';
  import { recordedBroadcastList } from '@/api/security/recordedBroadcast';
  import { formatToDateTime } from '@/utils/dateUtil';

  const ARadioGroup = ARadio.Group;
  const ARadioButton = ARadio.Button;

  defineOptions({ name: 'MonitorScreen' });

  interface StreamConfig {
    key: string;
    title: string;
    path: string;
    personChargeId: string;
    personChargeName: string;
    personChargeRoleType: string;
  }

  interface RecordedVideo {
    id: string;
    fileName: string;
    fileUrl: string;
    thumbnailUrl?: string;
    createTime: string;
    taskId: string;
    personChargeId?: string;
    personChargeName?: string;
    personChargeRoleType?: string;
  }

  interface PersonGroup {
    personKey: string;
    personName: string;
    videos: RecordedVideo[];
  }

  const props = defineProps({
    taskId: {
      type: [String, Number],
      required: true,
    },
  });

  // 响应式数据
  const monitorMode = ref<'live' | 'record'>('live');
  const liveStreams = ref<StreamConfig[]>([]);
  const recordedVideos = ref<RecordedVideo[]>([]);
  const previewVisible = ref(false);
  const currentPreviewVideo = ref<RecordedVideo | null>(null);
  const selectedVideo = ref<RecordedVideo | null>(null);

  // 计算属性 - 按人员分组的历史录像
  const groupedRecordedVideos = computed<PersonGroup[]>(() => {
    const groups = new Map<string, PersonGroup>();

    recordedVideos.value.forEach(video => {
      const personKey = video.personChargeId || 'unknown';
      const personName = video.personChargeName || '未知人员';

      if (!groups.has(personKey)) {
        groups.set(personKey, {
          personKey,
          personName,
          videos: [],
        });
      }

      groups.get(personKey)!.videos.push(video);
    });

    return Array.from(groups.values());
  });

  // 计算属性 - 显示的历史录像（最多4个）
  const displayVideos = computed(() => {
    return recordedVideos.value.slice(0, 4);
  });

  // 监听任务ID变化
  watch(() => props.taskId, (newTaskId) => {
    if (newTaskId) {
      loadData();
    }
  }, { immediate: true });

  // 加载数据
  async function loadData() {
    await Promise.all([
      loadLiveStreams(),
      loadRecordedVideos(),
    ]);
  }

  // 加载实时监控流
  async function loadLiveStreams() {
    try {
      const data = await postControlTaskGetLiveVideoUrl({ taskId: props.taskId });
      
      const roleMap = {
        leader: '工作领导人',
        contact: '驻站联络人',
        guardian: '地线监护人',
      };

      const streams = data
        .filter((item: any) => item.arGlassId)
        .map((item: any) => ({
          key: item.personChargeRoleType + item.personChargeId + item.arGlassId,
          title: `${item.personChargeName} - ${roleMap[item.personChargeRoleType]}`,
          path: `/${props.taskId}/${item.arGlassId}/${item.personChargeRoleType}/${item.personChargeId}`,
          personChargeId: item.personChargeId,
          personChargeName: item.personChargeName,
          personChargeRoleType: item.personChargeRoleType,
        }));

      liveStreams.value = streams;
    } catch (error) {
      console.error('加载实时监控流失败:', error);
      liveStreams.value = [];
    }
  }

  // 加载历史录像
  async function loadRecordedVideos() {
    try {
      const response = await recordedBroadcastList({
        taskId: props.taskId,
        pageNum: 1,
        pageSize: 1000, // 获取所有数据
      });
      
      recordedVideos.value = response.rows || [];
    } catch (error) {
      console.error('加载历史录像失败:', error);
      recordedVideos.value = [];
    }
  }

  // 模式切换处理
  function handleModeChange() {
    // 可以在这里添加切换时的逻辑
  }

  // 视频预览
  function handleVideoPreview(video: RecordedVideo) {
    currentPreviewVideo.value = video;
    previewVisible.value = true;
  }

  // 格式化时间
  function formatTime(time: string) {
    return formatToDateTime(time);
  }

  // 获取工作领导人姓名
  function getLeaderName() {
    const leader = liveStreams.value.find(stream => stream.personChargeRoleType === 'leader');
    return leader?.personChargeName || '张三';
  }

  // 选择视频
  function handleVideoSelect(video: RecordedVideo) {
    selectedVideo.value = video;
  }

  // 视频加载完成
  function handleVideoLoaded() {
    // 可以在这里添加视频加载完成后的逻辑
  }

  onMounted(() => {
    if (props.taskId) {
      loadData();
    }
  });
</script>

<style scoped lang="scss">
.monitor-screen {
  padding: 0;
}

.monitor-item {
  @apply bg-white rounded-lg overflow-hidden shadow-sm;
}

.person-label {
  @apply bg-gray-50 text-sm font-medium text-gray-700;
}

.video-container {
  @apply bg-black;
}

// 历史录像布局样式
.record-layout {
  @apply relative;

  padding: 10px;
  border-radius: 8px;
  background-color: #1a1a1a;
}

.leader-label {
  @apply absolute top-4 left-4 z-10;

  padding: 8px 16px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 80%);
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.main-monitor {
  @apply relative;

  height: 250px;
  margin-bottom: 10px;
  overflow: hidden;
  border-radius: 8px;
  background-color: #000;
}

.main-video-container {
  @apply w-full h-full;
}

.main-video {
  @apply w-full h-full object-cover;
}

.main-video-placeholder {
  @apply w-full h-full flex items-center justify-center;

  background-color: #2a2a2a;
}

.placeholder-content {
  @apply text-center text-gray-400;
}

.placeholder-icon {
  @apply text-6xl mb-4;
}

.placeholder-text {
  @apply text-lg;
}

.history-thumbnails {
  @apply flex gap-4 justify-center;
}

.history-thumbnail {
  @apply cursor-pointer transition-all duration-200;

  &:hover {
    transform: scale(1.05);

    .thumbnail-number {
      @apply hidden;
    }
  }

  &.active {
    .thumbnail-wrapper {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 30%);
    }
  }
}

.thumbnail-wrapper {
  @apply relative bg-gray-900 rounded overflow-hidden;

  width: 70px;
  height: 48px;
  transition: all 0.2s ease;
  border: 1px solid #fff;
}

.thumbnail-img {
  @apply w-full h-full object-cover;
}

.thumbnail-overlay {
  @apply absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 hover:opacity-100 transition-opacity;
}

.overlay-icon {
  @apply text-white text-xl;
}

.thumbnail-number {
  @apply absolute top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 bg-opacity-70 text-white text-lg text-shadow;
}

// 保留原有样式
.person-group {
  @apply bg-white rounded-lg overflow-hidden shadow-sm border;
}

.video-grid {
  @apply grid grid-cols-4 gap-3 p-4;
}

.video-thumbnail {
  @apply cursor-pointer transition-transform hover:scale-105;
}

.thumbnail-container {
  @apply relative bg-gray-900 rounded overflow-hidden;

  aspect-ratio: 16/9;
}

.thumbnail-image {
  @apply w-full h-full object-cover;
}

.play-overlay {
  @apply absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 hover:opacity-100 transition-opacity;
}

.play-icon {
  @apply text-white text-2xl;
}

.video-number {
  @apply absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-1.5 py-0.5 rounded;
}

.video-info {
  @apply mt-2;
}

.video-title {
  @apply text-sm font-medium text-gray-900 truncate;
}

.video-time {
  @apply text-xs text-gray-500 mt-1;
}

.empty-state {
  @apply h-full flex items-center justify-center text-gray-400;
}

.preview-container {
  @apply w-full;
}
</style>
